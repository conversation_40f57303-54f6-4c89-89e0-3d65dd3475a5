import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        
        // Primary Brand Colors
        "brand-rich-black": "var(--color-rich-black)",
        "brand-dark-green": "var(--color-dark-green)",
        "brand-bangladesh-green": "var(--color-bangladesh-green)",
        "brand-mountain-meadow": "var(--color-mountain-meadow)",
        "brand-caribbean-green": "var(--color-caribbean-green)",
        "brand-anti-flash-white": "var(--color-anti-flash-white)",
        
        // Secondary Brand Colors
        "brand-pine": "var(--color-pine)",
        "brand-basil": "var(--color-basil)",
        "brand-forest": "var(--color-forest)",
        "brand-frog": "var(--color-frog)",
        "brand-mint": "var(--color-mint)",
        "brand-stone": "var(--color-stone)",
        "brand-pistachio": "var(--color-pistachio)",
        
        // Legacy color mappings for backward compatibility
        "brand-pine-green": "var(--color-pine)",
      },
      fontFamily: {
        sans: ["var(--font-nunito)", "var(--font-geist-sans)", "Arial", "Helvetica", "sans-serif"],
        mono: ["var(--font-geist-mono)", "monospace"],
        nunito: ["var(--font-nunito)", "sans-serif"],
      },
      backgroundImage: {
        "gradient-primary": "var(--gradient-primary)",
        "gradient-hero": "var(--gradient-hero)",
        "gradient-dark": "var(--gradient-dark)",
        "gradient-overlay": "var(--gradient-overlay)",
      },
      animation: {
        "fade-in-up": "fadeInUp 0.6s ease-out forwards",
        "fade-in-left": "fadeInLeft 0.6s ease-out forwards",
        "fade-in-right": "fadeInRight 0.6s ease-out forwards",
        "scale-in": "scaleIn 0.5s ease-out forwards",
        "float": "float 3s ease-in-out infinite",
        "shimmer": "shimmer 2s infinite",
      },
      keyframes: {
        fadeInUp: {
          "0%": {
            opacity: "0",
            transform: "translateY(30px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        fadeInLeft: {
          "0%": {
            opacity: "0",
            transform: "translateX(-30px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateX(0)",
          },
        },
        fadeInRight: {
          "0%": {
            opacity: "0",
            transform: "translateX(30px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateX(0)",
          },
        },
        scaleIn: {
          "0%": {
            opacity: "0",
            transform: "scale(0.9)",
          },
          "100%": {
            opacity: "1",
            transform: "scale(1)",
          },
        },
        float: {
          "0%, 100%": {
            transform: "translateY(0px)",
          },
          "50%": {
            transform: "translateY(-10px)",
          },
        },
        shimmer: {
          "0%": {
            backgroundPosition: "-200% 0",
          },
          "100%": {
            backgroundPosition: "200% 0",
          },
        },
      },
    },
  },
  plugins: [],
};

export default config;
