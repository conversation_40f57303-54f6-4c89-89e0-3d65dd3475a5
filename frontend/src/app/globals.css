@import "tailwindcss";

:root {
  --background: #F5F7F6;
  --foreground: #032221;

  /* Comprehensive Sustainable Fashion Color Palette */

  /* Primary Colors */
  --color-rich-black: #00CF81;        /* Rich Black - Accents and highlights */
  --color-dark-green: #032221;        /* Dark Green - Primary background color */
  --color-bangladesh-green: #03E24C;  /* Bangladesh Green - Secondary accent for buttons and links */
  --color-mountain-meadow: #3CC395;   /* Mountain Meadow - Hero section gradient backgrounds */
  --color-caribbean-green: #00FF00;   /* Caribbean Green - Important CTAs and notifications */
  --color-anti-flash-white: #F5F7F6;  /* Anti-Flash White - Primary text color */

  /* Secondary Colors */
  --color-pine: #06302B;              /* Pine - Card backgrounds and sidebars */
  --color-basil: #08453A;             /* Basil - Secondary text color */
  --color-forest: #095544;            /* Forest - Hover effects and transitions */
  --color-frog: #178760;              /* Frog - Icons and decorative elements */
  --color-mint: #2A989C;              /* Mint - Community features highlights */
  --color-stone: #70D7D2;             /* Stone - Light section backgrounds */
  --color-pistachio: #AAC3C4;         /* Pistachio - Borders and inactive states */

  /* Gradient variations */
  --gradient-primary: linear-gradient(135deg, var(--color-dark-green) 0%, var(--color-mountain-meadow) 100%);
  --gradient-hero: linear-gradient(135deg, var(--color-dark-green) 0%, var(--color-mountain-meadow) 100%);
  --gradient-dark: linear-gradient(135deg, var(--color-dark-green) 0%, var(--color-pine) 100%);
  --gradient-overlay: linear-gradient(135deg, rgba(3, 34, 33, 0.9) 0%, rgba(6, 48, 43, 0.9) 100%);
}



@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--color-dark-green);
    --foreground: var(--color-anti-flash-white);
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-nunito), var(--font-sans), Arial, Helvetica, sans-serif;
  font-weight: 400;
}

/* Custom animations and transitions */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Smooth transitions for interactive elements */
.transition-all-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Gradient text effect */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Typography classes */
.font-heading {
  font-family: var(--font-nunito), sans-serif;
  font-weight: 600; /* Semi Bold */
}

.font-subheading {
  font-family: var(--font-nunito), sans-serif;
  font-weight: 500; /* Medium */
}

.font-body {
  font-family: var(--font-nunito), sans-serif;
  font-weight: 400; /* Regular */
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-stone);
}

::-webkit-scrollbar-thumb {
  background: var(--color-brand-pine);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-brand-forest);
}
