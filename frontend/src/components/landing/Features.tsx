'use client';

import { useEffect, useRef, useState } from 'react';
import {
  Recycle,
  Coins,
  Heart,
  Users,
  Leaf,
  Shield,
  Smartphone,
  TrendingUp,
  ArrowRight,
  Store,
  TreePine,
  BarChart3,
  Handshake,
  RefreshCw,
  Eye
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

const features = [
  {
    icon: Store,
    title: 'Sustainable Fashion Marketplace',
    description: 'Browse and discover eco-friendly fashion from verified sustainable brands and conscious consumers across Kenya.',
    color: 'text-brand-frog',
    bgColor: 'bg-brand-frog/10',
  },
  {
    icon: TreePine,
    title: 'Eco-Friendly Brand Partnerships',
    description: 'Shop from curated sustainable fashion brands committed to ethical production and environmental responsibility.',
    color: 'text-brand-forest',
    bgColor: 'bg-brand-forest/10',
  },
  {
    icon: BarChart3,
    title: 'Carbon Footprint Tracking',
    description: 'Monitor and reduce your fashion carbon footprint with detailed analytics on every purchase and exchange.',
    color: 'text-brand-mint',
    bgColor: 'bg-brand-mint/10',
  },
  {
    icon: Users,
    title: 'Community Sustainability Initiatives',
    description: 'Join local sustainability challenges, fashion swaps, and community events promoting circular fashion.',
    color: 'text-brand-bangladesh-green',
    bgColor: 'bg-brand-bangladesh-green/10',
  },
  {
    icon: RefreshCw,
    title: 'Circular Fashion Economy',
    description: 'Participate in clothing resale, upcycling workshops, and repair services to extend garment lifecycles.',
    color: 'text-brand-mountain-meadow',
    bgColor: 'bg-brand-mountain-meadow/10',
  },
  {
    icon: Eye,
    title: 'Supply Chain Transparency',
    description: 'Access detailed information about ethical sourcing, fair labor practices, and environmental impact of every item.',
    color: 'text-brand-caribbean-green',
    bgColor: 'bg-brand-caribbean-green/10',
  },
];

const benefits = [
  {
    icon: Smartphone,
    title: 'Mobile-First Design',
    description: 'Optimized for Kenya\'s mobile-first market with SMS notifications, M-Pesa integration, and offline capabilities.',
  },
  {
    icon: TrendingUp,
    title: 'AI Fashion Assistant',
    description: 'Get personalized sustainable fashion recommendations and styling advice powered by advanced AI technology.',
  },
  {
    icon: Shield,
    title: 'Secure M-Pesa Integration',
    description: 'Safe and seamless payments through M-Pesa with built-in buyer protection and transaction security.',
  },
];

export default function Features() {
  const [visibleItems, setVisibleItems] = useState<number[]>([]);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleItems(prev => [...prev, index]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = sectionRef.current?.querySelectorAll('[data-index]');
    elements?.forEach(el => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  return (
    <section id="features" ref={sectionRef} className="py-20 bg-brand-stone">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16" data-index="0">
          <h2 className={`text-3xl sm:text-4xl lg:text-5xl font-heading text-brand-dark-green mb-6 transition-all duration-1000 ${
            visibleItems.includes(0) ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            Why Choose <span className="gradient-text">Pedi</span>?
          </h2>
          <p className={`text-xl font-body text-brand-basil max-w-3xl mx-auto transition-all duration-1000 delay-200 ${
            visibleItems.includes(0) ? 'animate-fade-in-up opacity-100' : 'opacity-0'
          }`}>
            Discover the features that make Pedi Kenya's leading sustainable fashion platform
          </p>
        </div>

        {/* Main Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card
                key={index}
                data-index={index + 1}
                className={`p-8 hover-lift transition-all duration-1000 delay-${(index + 1) * 100} ${
                  visibleItems.includes(index + 1) ? 'animate-fade-in-up opacity-100' : 'opacity-0'
                } hover:shadow-xl border border-brand-pistachio/20 bg-brand-anti-flash-white rounded-2xl`}
              >
                <div className={`w-14 h-14 ${feature.bgColor} rounded-xl flex items-center justify-center mb-6 shadow-sm`}>
                  <Icon className={`h-7 w-7 ${feature.color}`} />
                </div>
                <h3 className="text-xl font-heading text-brand-dark-green mb-4">
                  {feature.title}
                </h3>
                <p className="text-brand-basil leading-relaxed font-body">
                  {feature.description}
                </p>
              </Card>
            );
          })}
        </div>

        {/* Additional Benefits */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div data-index="7" className={`transition-all duration-1000 ${
            visibleItems.includes(7) ? 'animate-fade-in-left opacity-100' : 'opacity-0'
          }`}>
            <h3 className="text-2xl sm:text-3xl font-heading text-brand-dark-green mb-8">
              Built for Kenya's Sustainable Fashion Future
            </h3>
            <div className="space-y-8">
              {benefits.map((benefit, index) => {
                const Icon = benefit.icon;
                return (
                  <div key={index} className="flex items-start space-x-5">
                    <div className="w-12 h-12 bg-brand-frog/10 rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm">
                      <Icon className="h-6 w-6 text-brand-frog" />
                    </div>
                    <div>
                      <h4 className="text-lg font-subheading text-brand-dark-green mb-3">
                        {benefit.title}
                      </h4>
                      <p className="text-brand-basil font-body leading-relaxed">
                        {benefit.description}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <div data-index="8" className={`transition-all duration-1000 delay-300 ${
            visibleItems.includes(8) ? 'animate-fade-in-right opacity-100' : 'opacity-0'
          }`}>
            <div className="bg-gradient-to-br from-brand-dark-green to-brand-pine rounded-3xl p-10 text-brand-anti-flash-white shadow-2xl border border-brand-pine/20">
              <h3 className="text-3xl font-heading mb-6">Ready to Transform Fashion in Kenya?</h3>
              <p className="text-brand-anti-flash-white/90 mb-8 leading-relaxed font-body text-lg">
                Join thousands of Kenyans who are already making sustainable fashion choices.
                Start your journey today and be part of the circular fashion revolution that's changing our country.
              </p>
              <Button
                size="lg"
                className="bg-brand-caribbean-green text-brand-dark-green hover:bg-brand-bangladesh-green font-subheading group shadow-lg hover:shadow-brand-caribbean-green/25 rounded-xl px-8 py-4"
              >
                Get Started Now
                <ArrowRight className="ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
